<template>
  <div class="low-altitude-calculation">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-content">
        <div class="logo">🛰️ 低空规模计算系统</div>
        <div class="user-info">
          <span>👤 {{ userInfo.name || '用户' }}</span>
          <span>📅 {{ currentDate }}</span>
          <el-button type="text" size="small" @click="showHelp">帮助</el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 左侧任务列表 -->
      <div class="task-panel">
        <div class="panel-header">
          <div class="panel-title">📋 计算任务</div>
          <el-button type="primary" size="small" @click="createNewTask">➕ 新建</el-button>
        </div>
        
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="🔍 搜索任务..."
            size="small"
            @input="handleSearch"
            clearable
          />
        </div>

        <div class="filter-box">
          <el-select v-model="statusFilter" placeholder="状态筛选" size="small" @change="loadTaskList">
            <el-option label="全部" value=""></el-option>
            <el-option label="待计算" value="0"></el-option>
            <el-option label="计算中" value="1"></el-option>
            <el-option label="计算成功" value="2"></el-option>
            <el-option label="计算失败" value="3"></el-option>
          </el-select>
        </div>
        
        <div class="task-list" v-loading="taskListLoading">
          <div
            v-for="task in taskList"
            :key="task.taskId"
            :class="['task-item', { active: selectedTask && selectedTask.taskId === task.taskId }]"
            @click="selectTask(task)"
          >
            <div class="task-name">{{ task.taskName }}</div>
            <div class="task-meta">
              <span>⏱️ {{ formatDate(task.createTime) }}</span>
              <span :class="getStatusClass(task.taskStatus)">{{ getStatusText(task.taskStatus) }}</span>
            </div>
            <div class="task-actions">
              <el-button type="text" size="mini" @click.stop="editTask(task)">✏️ 编辑</el-button>
              <el-button type="text" size="mini" @click.stop="deleteTask(task)">🗑️ 删除</el-button>
            </div>
          </div>
          
          <div v-if="taskList.length === 0" class="empty-state">
            <p>暂无任务数据</p>
            <el-button type="primary" @click="createNewTask">创建第一个任务</el-button>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-box">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            small
          />
        </div>
      </div>

      <!-- 中央表单区域 -->
      <div class="form-panel">
        <div class="form-container">
          <el-form
            ref="taskForm"
            :model="taskForm"
            :rules="taskRules"
            label-width="140px"
            size="small"
          >
            <!-- 任务信息 -->
            <div class="form-section">
              <div class="section-title">📝 任务信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="任务名称" prop="taskName">
                    <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="创建时间">
                    <el-input v-model="taskForm.createTime" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="任务描述">
                    <el-input
                      v-model="taskForm.taskDescription"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入任务描述"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 基本参数 -->
            <div class="form-section">
              <div class="section-title">🔧 基本参数</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="类型" prop="type">
                    <el-select v-model="taskForm.type" placeholder="请选择类型">
                      <el-option label="航线" value="航线" />
                      <el-option label="全空间立体覆盖" value="全空间立体覆盖" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="区域面积/航线长度" prop="areaOrLength">
                    <el-input-number
                      v-model="taskForm.areaOrLength"
                      :min="0.01"
                      :precision="2"
                      placeholder="请输入数值"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="航线高度" prop="routeHeight">
                    <el-input
                      v-model="taskForm.routeHeight"
                      placeholder="格式：50-100"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 技术参数 -->
            <div class="form-section">
              <div class="section-title">⚙️ 技术参数</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="技术体制" prop="technicalSystem">
                    <el-radio-group v-model="taskForm.technicalSystem">
                      <el-radio label="4G">4G</el-radio>
                      <el-radio label="5G">5G</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="使用频点 (MHz)" prop="frequency">
                    <el-input-number
                      v-model="taskForm.frequency"
                      :min="0.01"
                      :precision="2"
                      placeholder="请输入频点"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="SS RSRP发射功率 (dBm)" prop="transmitPower">
                    <el-input-number
                      v-model="taskForm.transmitPower"
                      :precision="2"
                      placeholder="请输入功率"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="SS RSRP接入门限功率 (dBm)" prop="accessThresholdPower">
                    <el-input-number
                      v-model="taskForm.accessThresholdPower"
                      :precision="2"
                      placeholder="请输入门限功率"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 业务参数 -->
            <div class="form-section">
              <div class="section-title">💼 业务参数</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="业务保障" prop="businessSupport">
                    <el-select v-model="taskForm.businessSupport" placeholder="请选择业务保障">
                      <el-option label="8K" value="8K" />
                      <el-option label="4K" value="4K" />
                      <el-option label="360P" value="360P" />
                      <el-option label="720P" value="720P" />
                      <el-option label="1080p" value="1080p" />
                      <el-option label="控制信息" value="控制信息" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="无人机密度" prop="droneDensity">
                    <el-input-number
                      v-model="taskForm.droneDensity"
                      :min="0"
                      placeholder="请输入密度"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="无线环境场景" prop="wirelessEnvironment">
                    <el-radio-group v-model="taskForm.wirelessEnvironment">
                      <el-radio label="郊区农村">郊区农村</el-radio>
                      <el-radio label="城区">城区</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="业务类型" prop="businessType">
                    <el-input v-model="taskForm.businessType" placeholder="请输入业务类型" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="占比" prop="proportion">
                    <div class="slider-container">
                      <span>0.0</span>
                      <el-slider
                        v-model="taskForm.proportion"
                        :min="0"
                        :max="1"
                        :step="0.1"
                        :format-tooltip="formatTooltip"
                        style="margin: 0 15px; flex: 1;"
                      />
                      <span>1.0</span>
                      <el-input-number
                        v-model="taskForm.proportion"
                        :min="0"
                        :max="1"
                        :step="0.1"
                        :precision="1"
                        size="small"
                        style="width: 80px; margin-left: 10px;"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
              <el-button
                type="primary"
                :loading="calculating"
                @click="calculateTask"
                :disabled="!selectedTask || selectedTask.taskStatus === 1"
              >
                🚀 {{ calculating ? '计算中...' : '开始计算' }}
              </el-button>
              <el-button type="success" @click="saveTask">💾 保存方案</el-button>
              <el-button @click="saveAndCalculate" :loading="calculating">
                ⚡ 保存并计算
              </el-button>
              <el-button @click="resetForm">🔄 重置表单</el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 右侧结果面板 -->
      <div class="result-panel">
        <div class="result-container">
          <div class="result-header">
            <h3>📊 计算结果</h3>
            <div v-if="calculationResult" class="result-status">
              <el-tag :type="getResultStatusType(selectedTask?.taskStatus)" size="small">
                {{ getStatusText(selectedTask?.taskStatus) }}
              </el-tag>
              <div class="result-time">
                ⏱️ 计算时间: {{ formatDateTime(selectedTask?.calculateEndTime) }}
              </div>
            </div>
          </div>

          <div v-if="calculationResult" class="result-content">
            <!-- 覆盖参数卡片 -->
            <div class="result-card">
              <div class="card-title">📡 布网覆盖半径</div>
              <div class="card-value">
                {{ formatNumber(calculationResult.networkCoverageRadius) }}
                <span class="card-unit">米</span>
              </div>
            </div>

            <div class="result-card">
              <div class="card-title">📏 布网站距</div>
              <div class="card-value">
                {{ formatNumber(calculationResult.networkStationDistance) }}
                <span class="card-unit">米</span>
              </div>
            </div>

            <div class="result-card">
              <div class="card-title">🏢 根据覆盖评估站点规模</div>
              <div class="card-value">
                {{ calculationResult.coverageAssessmentSiteScale }}
                <span class="card-unit">个</span>
              </div>
            </div>

            <!-- 容量评估卡片 -->
            <div class="result-card">
              <div class="card-title">📈 容量评估结果</div>
              <div class="card-value" :class="getCapacityResultClass(calculationResult.capacityAssessmentResult)">
                {{ calculationResult.capacityAssessmentResult }}
              </div>
            </div>

            <!-- 投资成本卡片 -->
            <div class="result-card">
              <div class="card-title">💰 投资（不含税）</div>
              <div class="card-value">
                {{ formatNumber(calculationResult.investmentExcludingTax) }}
                <span class="card-unit">元</span>
              </div>
            </div>

            <div class="result-card">
              <div class="card-title">💸 投资（含税）</div>
              <div class="card-value">
                {{ formatNumber(calculationResult.investmentIncludingTax) }}
                <span class="card-unit">元</span>
              </div>
            </div>

            <!-- 结果操作按钮 -->
            <div class="result-actions">
              <el-button type="primary" size="small" @click="exportExcel">📤 导出Excel</el-button>
              <el-button size="small" @click="copyResult">📋 复制结果</el-button>
              <el-button size="small" @click="generateReport">📊 生成报告</el-button>
              <el-button size="small" @click="recalculate">🔄 重新计算</el-button>
            </div>
          </div>

          <div v-else class="empty-result">
            <el-empty description="暂无计算结果">
              <el-button type="primary" @click="calculateTask">开始计算</el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="closeDialog"
    >
      <el-form
        ref="dialogForm"
        :model="dialogForm"
        :rules="taskRules"
        label-width="120px"
        size="small"
      >
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="dialogForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input
            v-model="dialogForm.taskDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDialog">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'LowAltitudeCalculation',
  data() {
    return {
      // 用户信息
      userInfo: {
        name: '张工程师'
      },
      currentDate: '',

      // 任务列表相关
      taskList: [],
      taskListLoading: false,
      selectedTask: null,
      searchKeyword: '',
      statusFilter: '',
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        taskStatus: null
      },
      total: 0,

      // 表单相关
      taskForm: {
        taskId: null,
        taskName: '',
        taskDescription: '',
        taskStatus: 0,
        type: '',
        areaOrLength: null,
        routeHeight: '',
        businessSupport: '',
        wirelessEnvironment: '',
        technicalSystem: '',
        frequency: null,
        transmitPower: null,
        droneDensity: null,
        businessType: '',
        proportion: 0.8,
        accessThresholdPower: null,
        createTime: ''
      },

      // 表单验证规则
      taskRules: {
        taskName: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        areaOrLength: [
          { required: true, message: '区域面积/航线长度不能为空', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '必须大于0', trigger: 'blur' }
        ],
        routeHeight: [
          { required: true, message: '航线高度不能为空', trigger: 'blur' },
          { pattern: /^\d+-\d+$/, message: '格式不正确，应为两个数字用-连接，如50-100', trigger: 'blur' }
        ],
        businessSupport: [
          { required: true, message: '请选择业务保障', trigger: 'change' }
        ],
        wirelessEnvironment: [
          { required: true, message: '请选择无线环境场景', trigger: 'change' }
        ],
        technicalSystem: [
          { required: true, message: '请选择技术体制', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '使用频点不能为空', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '必须大于0', trigger: 'blur' }
        ],
        transmitPower: [
          { required: true, message: 'SS RSRP发射功率不能为空', trigger: 'blur' }
        ],
        droneDensity: [
          { required: true, message: '无人机密度不能为空', trigger: 'blur' },
          { type: 'number', min: 0, message: '不能小于0', trigger: 'blur' }
        ],
        businessType: [
          { required: true, message: '业务类型不能为空', trigger: 'blur' }
        ],
        proportion: [
          { required: true, message: '占比不能为空', trigger: 'blur' },
          { type: 'number', min: 0, max: 1, message: '占比必须在0-1之间', trigger: 'blur' }
        ],
        accessThresholdPower: [
          { required: true, message: 'SS RSRP接入门限功率不能为空', trigger: 'blur' }
        ]
      },

      // 计算相关
      calculating: false,
      calculationResult: null,

      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      dialogForm: {
        taskId: null,
        taskName: '',
        taskDescription: ''
      },
      isEdit: false
    }
  },

  mounted() {
    this.updateCurrentDate()
    this.loadTaskList()
    // 每分钟更新一次时间
    setInterval(this.updateCurrentDate, 60000)
  },

  methods: {
    // 更新当前日期
    updateCurrentDate() {
      const now = new Date()
      this.currentDate = now.toLocaleDateString('zh-CN')
    },

    // 加载任务列表
    async loadTaskList() {
      this.taskListLoading = true
      try {
        const params = {
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          taskName: this.searchKeyword || null,
          taskStatus: this.statusFilter || null
        }

        const response = await axios.get('/dev-api/digital/task/list', { params })

        if (response.data.code === 200) {
          this.taskList = response.data.rows || []
          this.total = response.data.total || 0

          // 如果有选中的任务，更新选中状态
          if (this.selectedTask) {
            const updatedTask = this.taskList.find(task => task.taskId === this.selectedTask.taskId)
            if (updatedTask) {
              this.selectedTask = updatedTask
              this.loadTaskDetail(updatedTask.taskId)
            }
          }
        } else {
          this.$message.error(response.data.msg || '加载任务列表失败')
        }
      } catch (error) {
        console.error('加载任务列表失败:', error)
        this.$message.error('加载任务列表失败')
      } finally {
        this.taskListLoading = false
      }
    },

    // 搜索处理
    handleSearch() {
      this.queryParams.pageNum = 1
      this.loadTaskList()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.loadTaskList()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.loadTaskList()
    },

    // 选择任务
    selectTask(task) {
      this.selectedTask = task
      this.loadTaskDetail(task.taskId)
    },

    // 加载任务详情
    async loadTaskDetail(taskId) {
      try {
        const response = await axios.get(`/dev-api/digital/task/${taskId}`)

        if (response.data.code === 200) {
          const taskData = response.data.data

          // 填充表单数据
          this.taskForm = {
            taskId: taskData.taskId,
            taskName: taskData.taskName,
            taskDescription: taskData.taskDescription,
            taskStatus: taskData.taskStatus,
            type: taskData.type,
            areaOrLength: taskData.areaOrLength,
            routeHeight: taskData.routeHeight,
            businessSupport: taskData.businessSupport,
            wirelessEnvironment: taskData.wirelessEnvironment,
            technicalSystem: taskData.technicalSystem,
            frequency: taskData.frequency,
            transmitPower: taskData.transmitPower,
            droneDensity: taskData.droneDensity,
            businessType: taskData.businessType,
            proportion: taskData.proportion,
            accessThresholdPower: taskData.accessThresholdPower,
            createTime: this.formatDateTime(taskData.createTime)
          }

          // 加载计算结果
          if (response.data.calculationResult) {
            this.calculationResult = response.data.calculationResult
          } else {
            this.calculationResult = null
          }
        } else {
          this.$message.error(response.data.msg || '加载任务详情失败')
        }
      } catch (error) {
        console.error('加载任务详情失败:', error)
        this.$message.error('加载任务详情失败')
      }
    },

    // 创建新任务
    createNewTask() {
      this.dialogTitle = '新建任务'
      this.isEdit = false
      this.dialogForm = {
        taskId: null,
        taskName: '',
        taskDescription: ''
      }
      this.dialogVisible = true
    },

    // 编辑任务
    editTask(task) {
      this.dialogTitle = '编辑任务'
      this.isEdit = true
      this.dialogForm = {
        taskId: task.taskId,
        taskName: task.taskName,
        taskDescription: task.taskDescription
      }
      this.dialogVisible = true
    },

    // 删除任务
    deleteTask(task) {
      this.$confirm(`确定要删除任务"${task.taskName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await axios.delete(`/dev-api/digital/task/${task.taskId}`)

          if (response.data.code === 200) {
            this.$message.success('删除成功')

            // 如果删除的是当前选中的任务，清空选中状态
            if (this.selectedTask && this.selectedTask.taskId === task.taskId) {
              this.selectedTask = null
              this.calculationResult = null
              this.resetForm()
            }

            this.loadTaskList()
          } else {
            this.$message.error(response.data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除任务失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 提交对话框
    submitDialog() {
      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (this.isEdit) {
              // 编辑任务
              response = await axios.put('/dev-api/digital/task', this.dialogForm)
            } else {
              // 新建任务
              response = await axios.post('/dev-api/digital/task', this.dialogForm)
            }

            if (response.data.code === 200) {
              this.$message.success(this.isEdit ? '编辑成功' : '创建成功')
              this.dialogVisible = false
              this.loadTaskList()

              // 如果是新建任务，选中新创建的任务
              if (!this.isEdit && response.data.data) {
                setTimeout(() => {
                  const newTask = this.taskList.find(task => task.taskId === response.data.data)
                  if (newTask) {
                    this.selectTask(newTask)
                  }
                }, 500)
              }
            } else {
              this.$message.error(response.data.msg || (this.isEdit ? '编辑失败' : '创建失败'))
            }
          } catch (error) {
            console.error('提交失败:', error)
            this.$message.error(this.isEdit ? '编辑失败' : '创建失败')
          }
        }
      })
    },

    // 关闭对话框
    closeDialog() {
      this.$refs.dialogForm.resetFields()
    },

    // 保存任务
    saveTask() {
      this.$refs.taskForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (this.taskForm.taskId) {
              // 更新任务
              response = await axios.put('/dev-api/digital/task', this.taskForm)
            } else {
              // 创建任务
              response = await axios.post('/dev-api/digital/task', this.taskForm)
            }

            if (response.data.code === 200) {
              this.$message.success('保存成功')
              this.loadTaskList()

              // 如果是新建任务，更新taskId
              if (!this.taskForm.taskId && response.data.data) {
                this.taskForm.taskId = response.data.data
              }
            } else {
              this.$message.error(response.data.msg || '保存失败')
            }
          } catch (error) {
            console.error('保存失败:', error)
            this.$message.error('保存失败')
          }
        }
      })
    },

    // 计算任务
    async calculateTask() {
      if (!this.selectedTask) {
        this.$message.warning('请先选择一个任务')
        return
      }

      this.calculating = true
      try {
        const response = await axios.post(`/dev-api/digital/task/calculate/${this.selectedTask.taskId}`)

        if (response.data.code === 200) {
          this.$message.success('计算成功')
          this.calculationResult = response.data.data

          // 更新任务状态
          this.selectedTask.taskStatus = 2
          this.loadTaskList()
        } else {
          this.$message.error(response.data.msg || '计算失败')
        }
      } catch (error) {
        console.error('计算失败:', error)
        this.$message.error('计算失败')
      } finally {
        this.calculating = false
      }
    },

    // 保存并计算
    saveAndCalculate() {
      this.$refs.taskForm.validate(async (valid) => {
        if (valid) {
          this.calculating = true
          try {
            const response = await axios.post('/dev-api/digital/task/saveAndCalculate', this.taskForm)

            if (response.data.code === 200) {
              this.$message.success('保存并计算成功')
              this.calculationResult = response.data.calculationResult

              // 更新任务ID和状态
              if (response.data.taskId) {
                this.taskForm.taskId = response.data.taskId
              }

              this.loadTaskList()
            } else {
              this.$message.error(response.data.msg || '保存并计算失败')
            }
          } catch (error) {
            console.error('保存并计算失败:', error)
            this.$message.error('保存并计算失败')
          } finally {
            this.calculating = false
          }
        }
      })
    },

    // 重新计算
    recalculate() {
      this.calculateTask()
    },

    // 重置表单
    resetForm() {
      this.$refs.taskForm.resetFields()
      this.taskForm = {
        taskId: null,
        taskName: '',
        taskDescription: '',
        taskStatus: 0,
        type: '',
        areaOrLength: null,
        routeHeight: '',
        businessSupport: '',
        wirelessEnvironment: '',
        technicalSystem: '',
        frequency: null,
        transmitPower: null,
        droneDensity: null,
        businessType: '',
        proportion: 0.8,
        accessThresholdPower: null,
        createTime: ''
      }
      this.calculationResult = null
      this.selectedTask = null
    },

    // 导出Excel
    exportExcel() {
      if (!this.calculationResult) {
        this.$message.warning('暂无计算结果可导出')
        return
      }

      // 这里可以调用后端导出接口或前端生成Excel
      this.$message.info('导出功能开发中...')
    },

    // 复制结果
    copyResult() {
      if (!this.calculationResult) {
        this.$message.warning('暂无计算结果可复制')
        return
      }

      const resultText = `
布网覆盖半径: ${this.formatNumber(this.calculationResult.networkCoverageRadius)} 米
布网站距: ${this.formatNumber(this.calculationResult.networkStationDistance)} 米
根据覆盖评估站点规模: ${this.calculationResult.coverageAssessmentSiteScale} 个
容量评估结果: ${this.calculationResult.capacityAssessmentResult}
投资（不含税）: ${this.formatNumber(this.calculationResult.investmentExcludingTax)} 元
投资（含税）: ${this.formatNumber(this.calculationResult.investmentIncludingTax)} 元
      `.trim()

      // 复制到剪贴板
      navigator.clipboard.writeText(resultText).then(() => {
        this.$message.success('结果已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 生成报告
    generateReport() {
      this.$message.info('报告生成功能开发中...')
    },

    // 显示帮助
    showHelp() {
      this.$message.info('帮助文档开发中...')
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.toLocaleDateString('zh-CN')
    },

    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.toLocaleString('zh-CN')
    },

    // 格式化数字（保留两位小数）
    formatNumber(num) {
      if (num === null || num === undefined) return '0.00'
      return Number(num).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },

    // 格式化滑块提示
    formatTooltip(val) {
      return val.toFixed(1)
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待计算',
        1: '计算中',
        2: '✅ 成功',
        3: '❌ 失败'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: 'status-pending',
        1: 'status-calculating',
        2: 'status-success',
        3: 'status-failed'
      }
      return classMap[status] || ''
    },

    // 获取结果状态类型
    getResultStatusType(status) {
      const typeMap = {
        0: 'info',
        1: 'warning',
        2: 'success',
        3: 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 获取容量评估结果样式类
    getCapacityResultClass(result) {
      if (result && result.includes('满足')) {
        return 'capacity-success'
      } else if (result && result.includes('不满足')) {
        return 'capacity-failed'
      }
      return ''
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
.low-altitude-calculation {
  height: 100vh;
  background-color: #f5f5f5;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

/* 顶部导航栏 */
.header {
  background: linear-gradient(135deg, #1976D2, #1565C0);
  color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.logo {
  font-size: 24px;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 主要内容区域 */
.main-container {
  max-width: 1400px;
  margin: 20px auto;
  display: grid;
  grid-template-columns: 350px 1fr 400px;
  gap: 20px;
  padding: 0 20px;
  height: calc(100vh - 120px);
}

/* 左侧任务面板 */
.task-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.search-box, .filter-box {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
}

.task-list {
  flex: 1;
  overflow-y: auto;
}

.task-item {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.task-item:hover {
  background-color: #f8f9fa;
}

.task-item.active {
  background-color: #e3f2fd;
  border-left: 4px solid #1976D2;
}

.task-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.task-meta {
  font-size: 12px;
  color: #666;
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.task-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.pagination-box {
  padding: 15px;
  border-top: 1px solid #e9ecef;
}

/* 状态样式 */
.status-success {
  color: #4CAF50;
}

.status-failed {
  color: #F44336;
}

.status-pending {
  color: #FF9800;
}

.status-calculating {
  color: #2196F3;
}

/* 中央表单区域 */
.form-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.form-container {
  padding: 20px;
}

.form-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #1976D2;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

/* 右侧结果面板 */
.result-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.result-container {
  padding: 20px;
}

.result-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.result-status {
  margin-top: 10px;
}

.result-time {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.result-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #1976D2;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.card-unit {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}

.capacity-success {
  color: #4CAF50;
}

.capacity-failed {
  color: #F44336;
}

.result-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
}

.empty-result {
  text-align: center;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    height: auto;
  }

  .task-panel, .form-panel, .result-panel {
    height: auto;
    max-height: 500px;
  }
}
</style>
