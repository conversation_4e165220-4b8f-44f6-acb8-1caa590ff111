package com.startel.digital.config;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 计算专用线程池配置
 * 
 * @description 为低空规模计算任务提供专用的线程池配置
 * <AUTHOR>
 * @date 2024-12-28
 */
@Configuration
public class CalculationThreadPoolConfig {
    
    private static final Logger log = LoggerFactory.getLogger(CalculationThreadPoolConfig.class);
    
    // 计算线程池配置参数（针对I/O密集型任务优化）
    private static final int CORE_POOL_SIZE = 3;           // 核心线程数（适中）
    private static final int MAXIMUM_POOL_SIZE = 8;        // 最大线程数（适度增加）
    private static final long KEEP_ALIVE_TIME_SECONDS = 120; // 空闲时间（2分钟）
    private static final int QUEUE_CAPACITY = 50;          // 队列大小（适中）
    
    /**
     * 低空规模计算专用线程池
     */
    @Bean(name = "calculationThreadPoolExecutor")
    public ThreadPoolExecutor calculationThreadPoolExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAXIMUM_POOL_SIZE,
            KEEP_ALIVE_TIME_SECONDS,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            new BasicThreadFactory.Builder()
                .namingPattern("LowAltitudeCalc-%d")
                .daemon(true)
                .priority(Thread.NORM_PRIORITY)
                .build(),
            new ThreadPoolExecutor.CallerRunsPolicy() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                    log.warn("计算任务被拒绝执行，当前线程池状态: active={}, pool={}, queue={}", 
                        e.getActiveCount(), e.getPoolSize(), e.getQueue().size());
                    super.rejectedExecution(r, e);
                }
            }
        ) {
            @Override
            protected void beforeExecute(Thread t, Runnable r) {
                super.beforeExecute(t, r);
                log.debug("开始执行计算任务，线程: {}", t.getName());
            }
            
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                if (t != null) {
                    log.error("计算任务执行异常", t);
                } else {
                    log.debug("计算任务执行完成");
                }
            }
            
            @Override
            protected void terminated() {
                super.terminated();
                log.info("计算线程池已关闭");
            }
        };
        
        // 允许核心线程超时
        executor.allowCoreThreadTimeOut(true);
        
        log.info("计算专用线程池初始化完成: core={}, max={}, queue={}, keepAlive={}s", 
            CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, QUEUE_CAPACITY, KEEP_ALIVE_TIME_SECONDS);
        
        return executor;
    }
    
    /**
     * 计算任务监控线程池
     */
    @Bean(name = "calculationMonitorExecutor")
    public ScheduledExecutorService calculationMonitorExecutor() {
        ScheduledExecutorService executor = new ScheduledThreadPoolExecutor(
            1, // 单线程即可
            new BasicThreadFactory.Builder()
                .namingPattern("CalcMonitor-%d")
                .daemon(true)
                .priority(Thread.MIN_PRIORITY)
                .build(),
            new ThreadPoolExecutor.DiscardPolicy() // 监控任务可以丢弃
        ) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                if (t != null) {
                    log.warn("计算监控任务执行异常", t);
                }
            }
        };
        
        log.info("计算监控线程池初始化完成");
        return executor;
    }
    
    /**
     * 获取线程池状态信息
     */
    public static String getThreadPoolStatus(ThreadPoolExecutor executor) {
        if (executor == null) {
            return "ThreadPool[Not Available]";
        }
        
        return String.format(
            "ThreadPool[Active: %d, Pool: %d, Core: %d, Max: %d, Queue: %d, Completed: %d, Task: %d]",
            executor.getActiveCount(),
            executor.getPoolSize(),
            executor.getCorePoolSize(),
            executor.getMaximumPoolSize(),
            executor.getQueue().size(),
            executor.getCompletedTaskCount(),
            executor.getTaskCount()
        );
    }
    
    /**
     * 获取线程池健康状态
     */
    public static boolean isThreadPoolHealthy(ThreadPoolExecutor executor) {
        if (executor == null || executor.isShutdown()) {
            return false;
        }
        
        // 检查线程池是否过载
        double queueUsageRate = (double) executor.getQueue().size() / QUEUE_CAPACITY;
        double threadUsageRate = (double) executor.getActiveCount() / executor.getMaximumPoolSize();
        
        // 队列使用率超过80%或线程使用率超过90%认为不健康
        return queueUsageRate < 0.8 && threadUsageRate < 0.9;
    }
}
