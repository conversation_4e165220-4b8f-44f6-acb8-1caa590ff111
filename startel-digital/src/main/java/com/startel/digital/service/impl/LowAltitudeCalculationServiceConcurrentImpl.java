package com.startel.digital.service.impl;

import com.alibaba.fastjson.JSON;
import com.startel.common.exception.ServiceException;
import com.startel.common.utils.DateUtils;
import com.startel.common.utils.uuid.IdUtils;
import com.startel.digital.domain.LowAltitudeCalculationRequest;
import com.startel.digital.domain.LowAltitudeCalculationResponse;
import com.startel.digital.domain.LowAltitudeTask;
import com.startel.digital.service.ILowAltitudeCalculationService;
import com.startel.digital.service.ILowAltitudeTaskService;
import com.startel.digital.utils.ExcelFileOperationUtils;
import com.startel.digital.utils.FileOperationDiagnostics;
import com.startel.digital.utils.TempFileManager;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 低空规模计算服务并发安全实现类
 *
 * @description 解决多用户并发访问Excel文件的安全问题
 * <AUTHOR>
 * @date 2024-12-28
 */
@Service
@Primary
public class LowAltitudeCalculationServiceConcurrentImpl implements ILowAltitudeCalculationService {
    
    private static final Logger log = LoggerFactory.getLogger(LowAltitudeCalculationServiceConcurrentImpl.class);
    
    @Autowired
    private ILowAltitudeTaskService lowAltitudeTaskService;

    @Autowired
    private TempFileManager tempFileManager;
    
    // Excel文件相关常量
    private static final String EXCEL_FILE_PATH = "低空规模计算V2.xlsx";
    
    // 单元格位置常量
    private static final int INPUT_COLUMN = 3; // D列
    private static final int OUTPUT_COLUMN = 8; // I列
    private static final int TYPE_ROW = 4;
    private static final int AREA_OR_LENGTH_ROW = 5;
    private static final int ROUTE_HEIGHT_ROW = 6;
    private static final int BUSINESS_SUPPORT_ROW = 7;
    private static final int WIRELESS_ENVIRONMENT_ROW = 8;
    private static final int TECHNICAL_SYSTEM_ROW = 9;
    private static final int FREQUENCY_ROW = 10;
    private static final int TRANSMIT_POWER_ROW = 11;
    private static final int DRONE_DENSITY_ROW = 12;
    private static final int BUSINESS_TYPE_ROW = 13;
    private static final int PROPORTION_ROW = 14;
    private static final int ACCESS_THRESHOLD_POWER_ROW = 15;
    
    private static final int NETWORK_COVERAGE_RADIUS_ROW = 4;
    private static final int NETWORK_STATION_DISTANCE_ROW = 5;
    private static final int COVERAGE_ASSESSMENT_SITE_SCALE_ROW = 6;
    private static final int CAPACITY_ASSESSMENT_RESULT_ROW = 7;
    private static final int INVESTMENT_EXCLUDING_TAX_ROW = 8;
    private static final int INVESTMENT_INCLUDING_TAX_ROW = 9;
    
    // 并发控制相关
    private final ReentrantLock calculationLock = new ReentrantLock(true); // 公平锁
    private ExecutorService calculationExecutor;
    private final ConcurrentHashMap<Long, Future<LowAltitudeCalculationResponse>> runningCalculations = new ConcurrentHashMap<>();
    
    // 硬编码配置参数（移除外部配置依赖）
    private static final int MAX_CONCURRENT_CALCULATIONS = 5; // 最大并发计算数
    private static final long CALCULATION_TIMEOUT_MINUTES = 10; // 计算超时时间（分钟）
    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    private static final int CORE_POOL_SIZE = 2; // 核心线程数
    private static final int MAXIMUM_POOL_SIZE = 5; // 最大线程数
    private static final long KEEP_ALIVE_TIME_SECONDS = 60; // 线程空闲时间
    private static final int QUEUE_CAPACITY = 100; // 任务队列大小
    private static final int TEMP_FILE_CLEANUP_INTERVAL_HOURS = 24; // 临时文件清理间隔
    private static final int TEMP_FILE_MAX_AGE_HOURS = 2; // 临时文件最大保留时间
    
    @PostConstruct
    public void init() {
        // 初始化线程池（使用硬编码配置）
        calculationExecutor = new ThreadPoolExecutor(
            CORE_POOL_SIZE, // 核心线程数
            MAXIMUM_POOL_SIZE, // 最大线程数
            KEEP_ALIVE_TIME_SECONDS, TimeUnit.SECONDS, // 空闲线程存活时间
            new LinkedBlockingQueue<>(QUEUE_CAPACITY), // 任务队列
            new ThreadFactory() {
                private int counter = 0;
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "LowAltitudeCalculation-" + (++counter));
                    thread.setDaemon(true);
                    return thread;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
        
        // 验证临时文件管理器
        if (!tempFileManager.validateTempDirectory()) {
            log.error("临时文件管理器验证失败");
        } else {
            log.info("临时文件管理器初始化成功: {}", tempFileManager.getTempDirectoryPath());
        }
    }
    
    @PreDestroy
    public void destroy() {
        if (calculationExecutor != null) {
            calculationExecutor.shutdown();
            try {
                if (!calculationExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    calculationExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                calculationExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 清理临时文件
        if (tempFileManager != null) {
            tempFileManager.cleanupExpiredFiles(TEMP_FILE_MAX_AGE_HOURS);
        }
    }

    @Override
    public LowAltitudeCalculationResponse calculate(LowAltitudeCalculationRequest request) throws Exception {
        return calculateWithTask(request, null);
    }
    
    /**
     * 执行低空规模计算（带任务管理和并发控制）
     */
    @Override
    public LowAltitudeCalculationResponse calculateWithTask(LowAltitudeCalculationRequest request, Long taskId) throws Exception {
        log.info("开始执行低空规模计算，请求参数：{}，任务ID：{}", request, taskId);
        
        // 检查是否已有相同任务在计算中
        if (taskId != null && runningCalculations.containsKey(taskId)) {
            throw new ServiceException("任务正在计算中，请稍后再试");
        }
        
        LowAltitudeTask task = null;
        if (taskId != null) {
            task = lowAltitudeTaskService.selectLowAltitudeTaskByTaskId(taskId);
            if (task != null) {
                // 更新任务状态为计算中
                task.setTaskStatus(1);
                task.setCalculateStartTime(DateUtils.getNowDate());
                task.setErrorMessage(null);
                lowAltitudeTaskService.updateLowAltitudeTask(task);
            }
        }
        
        // 提交计算任务到线程池
        Future<LowAltitudeCalculationResponse> future = calculationExecutor.submit(
            new CalculationTask(request, task)
        );
        
        if (taskId != null) {
            runningCalculations.put(taskId, future);
        }
        
        try {
            // 等待计算完成，设置超时时间
            LowAltitudeCalculationResponse response = future.get(CALCULATION_TIMEOUT_MINUTES, TimeUnit.MINUTES);
            
            // 更新任务状态为计算成功
            if (task != null) {
                task.setTaskStatus(2);
                task.setCalculateEndTime(DateUtils.getNowDate());
                task.setResultJson(JSON.toJSONString(response));
                lowAltitudeTaskService.updateLowAltitudeTask(task);
            }
            
            log.info("低空规模计算完成，计算结果：{}", response);
            return response;
            
        } catch (TimeoutException e) {
            log.error("计算超时，任务ID：{}", taskId);
            future.cancel(true);
            
            if (task != null) {
                task.setTaskStatus(3);
                task.setCalculateEndTime(DateUtils.getNowDate());
                task.setErrorMessage("计算超时");
                lowAltitudeTaskService.updateLowAltitudeTask(task);
            }
            
            throw new ServiceException("计算超时，请检查参数后重试");
            
        } catch (Exception e) {
            log.error("低空规模计算失败，任务ID：{}", taskId, e);
            
            if (task != null) {
                task.setTaskStatus(3);
                task.setCalculateEndTime(DateUtils.getNowDate());
                task.setErrorMessage(e.getMessage());
                lowAltitudeTaskService.updateLowAltitudeTask(task);
            }
            
            throw new ServiceException("低空规模计算失败：" + e.getMessage());
            
        } finally {
            if (taskId != null) {
                runningCalculations.remove(taskId);
            }
        }
    }
    
    /**
     * 计算任务类
     */
    private class CalculationTask implements Callable<LowAltitudeCalculationResponse> {
        private final LowAltitudeCalculationRequest request;
        private final LowAltitudeTask task;
        
        public CalculationTask(LowAltitudeCalculationRequest request, LowAltitudeTask task) {
            this.request = request;
            this.task = task;
        }
        
        @Override
        public LowAltitudeCalculationResponse call() throws Exception {
            return executeCalculationWithRetry();
        }
        
        /**
         * 带重试机制的计算执行（增强版）
         */
        private LowAltitudeCalculationResponse executeCalculationWithRetry() throws Exception {
            Exception lastException = null;

            for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
                try {
                    log.info("开始计算尝试 {}/{}", attempt, MAX_RETRY_ATTEMPTS);
                    return executeCalculationSafely();

                } catch (Exception e) {
                    lastException = e;
                    String errorMsg = e.getMessage();

                    log.warn("计算尝试 {}/{} 失败: {}", attempt, MAX_RETRY_ATTEMPTS, errorMsg);

                    // 如果是第一次失败，进行详细诊断
                    if (attempt == 1) {
                        try {
                            String uuid = IdUtils.fastUUID();
                            Path tempFilePath = tempFileManager.createCalculationTempFilePath(uuid);
                            log.error("文件操作诊断信息:");
                            FileOperationDiagnostics.printDiagnosis(tempFilePath.getParent());

                            String advice = FileOperationDiagnostics.getFileOperationAdvice(tempFilePath, e);
                            log.error("操作建议:\n{}", advice);
                        } catch (Exception diagException) {
                            log.warn("诊断失败: {}", diagException.getMessage());
                        }
                    }

                    // 根据错误类型决定是否继续重试
                    if (shouldRetry(e, attempt)) {
                        long delay = calculateRetryDelay(attempt, e);
                        log.info("等待 {}ms 后进行第 {} 次重试", delay, attempt + 1);

                        try {
                            Thread.sleep(delay);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new ServiceException("计算被中断");
                        }
                    } else {
                        log.error("错误类型不适合重试，直接失败: {}", errorMsg);
                        break;
                    }
                }
            }

            throw new ServiceException("计算失败，已重试" + MAX_RETRY_ATTEMPTS + "次: " + lastException.getMessage());
        }

        /**
         * 判断是否应该重试
         */
        private boolean shouldRetry(Exception e, int attempt) {
            if (attempt >= MAX_RETRY_ATTEMPTS) {
                return false;
            }

            String message = e.getMessage();
            if (message == null) {
                return true;
            }

            message = message.toLowerCase();

            // 文件占用相关错误可以重试
            if (message.contains("being used") ||
                message.contains("另一个程序正在使用") ||
                message.contains("已设定文件的一部分") ||
                message.contains("locked") ||
                message.contains("文件锁")) {
                return true;
            }

            // 权限错误通常不需要重试
            if (message.contains("access denied") ||
                message.contains("拒绝访问") ||
                message.contains("权限不足")) {
                return false;
            }

            // 文件不存在错误不需要重试
            if (message.contains("no such file") ||
                message.contains("找不到文件")) {
                return false;
            }

            // 其他错误可以重试
            return true;
        }

        /**
         * 计算重试延迟时间
         */
        private long calculateRetryDelay(int attempt, Exception e) {
            long baseDelay = 2000; // 基础延迟2秒

            String message = e.getMessage();
            if (message != null && message.toLowerCase().contains("locked")) {
                // 文件锁冲突，使用较长的延迟
                return baseDelay * attempt * 2;
            }

            // 其他错误使用标准延迟
            return baseDelay * attempt;
        }
        
        /**
         * 安全的计算执行（使用临时文件隔离）
         */
        private LowAltitudeCalculationResponse executeCalculationSafely() throws Exception {
            // 生成唯一的临时文件路径
            String uuid = IdUtils.fastUUID();
            Path tempFilePath = tempFileManager.createCalculationTempFilePath(uuid);
            
            try {
                // 复制模板文件到临时文件
                copyTemplateToTempFile(tempFilePath);
                
                // 使用文件锁进行计算
                return calculateWithFileLock(tempFilePath);
                
            } finally {
                // 清理临时文件
                try {
                    Files.deleteIfExists(tempFilePath);
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", tempFilePath, e);
                }
            }
        }
        
        /**
         * 复制模板文件到临时文件
         */
        private void copyTemplateToTempFile(Path tempFilePath) throws IOException {
            ClassPathResource resource = new ClassPathResource(EXCEL_FILE_PATH);
            if (!resource.exists()) {
                throw new ServiceException("Excel模板文件不存在：" + EXCEL_FILE_PATH);
            }
            
            try (InputStream inputStream = resource.getInputStream()) {
                Files.copy(inputStream, tempFilePath, StandardCopyOption.REPLACE_EXISTING);
            }
        }
        
        /**
         * 使用文件锁进行计算（修复版本）
         */
        private LowAltitudeCalculationResponse calculateWithFileLock(Path tempFilePath) throws Exception {
            log.debug("开始执行Excel计算，文件: {}", tempFilePath.getFileName());

            // 验证临时文件
            if (!java.nio.file.Files.exists(tempFilePath)) {
                throw new ServiceException("临时文件不存在: " + tempFilePath);
            }

            // 使用工具类执行安全的Excel操作
            return ExcelFileOperationUtils.executeWithFileLock(tempFilePath, (filePath, raf, channel, lock) -> {
                log.debug("获取文件锁成功，开始Excel操作: {}", filePath.getFileName());

                // 使用已有的RandomAccessFile读取Excel工作簿（避免文件锁冲突）
                Workbook workbook = ExcelFileOperationUtils.readWorkbookFromRandomAccessFile(raf);

                try {
                    // 获取第一个工作表
                    Sheet sheet = workbook.getSheetAt(0);
                    if (sheet == null) {
                        throw new ServiceException("Excel文件中没有找到工作表");
                    }

                    log.debug("成功读取Excel工作表，开始写入输入参数");

                    // 写入输入参数到Excel
                    writeInputDataToExcel(sheet, request);

                    log.debug("输入参数写入完成，开始重新计算公式");

                    // 强制重新计算公式
                    workbook.getCreationHelper().createFormulaEvaluator().evaluateAll();

                    log.debug("公式计算完成，保存Excel文件");

                    // 使用已有的RandomAccessFile保存修改（避免文件锁冲突）
                    ExcelFileOperationUtils.saveWorkbookToRandomAccessFile(workbook, raf);

                    log.debug("Excel文件保存完成，开始读取计算结果");

                    // 读取计算结果
                    LowAltitudeCalculationResponse response = readOutputDataFromExcel(sheet);

                    log.debug("计算结果读取完成: {}", response);

                    return response;

                } finally {
                    if (workbook != null) {
                        try {
                            workbook.close();
                        } catch (Exception e) {
                            log.warn("关闭Excel工作簿失败: {}", e.getMessage());
                        }
                    }
                }
            });
        }
    }
    
    /**
     * 将输入参数写入Excel文件（使用安全工具类）
     */
    private void writeInputDataToExcel(Sheet sheet, LowAltitudeCalculationRequest request) {
        try {
            ExcelFileOperationUtils.setCellValue(sheet, TYPE_ROW, INPUT_COLUMN, request.getType());
            ExcelFileOperationUtils.setCellValue(sheet, AREA_OR_LENGTH_ROW, INPUT_COLUMN, request.getAreaOrLength());
            ExcelFileOperationUtils.setCellValue(sheet, ROUTE_HEIGHT_ROW, INPUT_COLUMN, request.getRouteHeight());
            ExcelFileOperationUtils.setCellValue(sheet, BUSINESS_SUPPORT_ROW, INPUT_COLUMN, request.getBusinessSupport());
            ExcelFileOperationUtils.setCellValue(sheet, WIRELESS_ENVIRONMENT_ROW, INPUT_COLUMN, request.getWirelessEnvironment());
            ExcelFileOperationUtils.setCellValue(sheet, TECHNICAL_SYSTEM_ROW, INPUT_COLUMN, request.getTechnicalSystem());
            ExcelFileOperationUtils.setCellValue(sheet, FREQUENCY_ROW, INPUT_COLUMN, request.getFrequency());
            ExcelFileOperationUtils.setCellValue(sheet, TRANSMIT_POWER_ROW, INPUT_COLUMN, request.getTransmitPower());
            ExcelFileOperationUtils.setCellValue(sheet, DRONE_DENSITY_ROW, INPUT_COLUMN, request.getDroneDensity());
            ExcelFileOperationUtils.setCellValue(sheet, BUSINESS_TYPE_ROW, INPUT_COLUMN, request.getBusinessType());
            ExcelFileOperationUtils.setCellValue(sheet, PROPORTION_ROW, INPUT_COLUMN, request.getProportion());
            ExcelFileOperationUtils.setCellValue(sheet, ACCESS_THRESHOLD_POWER_ROW, INPUT_COLUMN, request.getAccessThresholdPower());

            log.debug("成功写入所有输入参数到Excel");
        } catch (Exception e) {
            log.error("写入输入参数到Excel失败", e);
            throw new ServiceException("写入输入参数失败: " + e.getMessage());
        }
    }
    
    /**
     * 从Excel文件读取输出结果（使用安全工具类）
     */
    private LowAltitudeCalculationResponse readOutputDataFromExcel(Sheet sheet) {
        try {
            LowAltitudeCalculationResponse response = new LowAltitudeCalculationResponse();

            // 保留两位小数的字段
            response.setNetworkCoverageRadius(formatToTwoDecimalPlaces(ExcelFileOperationUtils.getCellDoubleValue(sheet, NETWORK_COVERAGE_RADIUS_ROW, OUTPUT_COLUMN)));
            response.setNetworkStationDistance(formatToTwoDecimalPlaces(ExcelFileOperationUtils.getCellDoubleValue(sheet, NETWORK_STATION_DISTANCE_ROW, OUTPUT_COLUMN)));
            response.setCoverageAssessmentSiteScale(ExcelFileOperationUtils.getCellIntegerValue(sheet, COVERAGE_ASSESSMENT_SITE_SCALE_ROW, OUTPUT_COLUMN));
            response.setCapacityAssessmentResult(ExcelFileOperationUtils.getCellStringValue(sheet, CAPACITY_ASSESSMENT_RESULT_ROW, OUTPUT_COLUMN));
            response.setInvestmentExcludingTax(formatToTwoDecimalPlaces(ExcelFileOperationUtils.getCellDoubleValue(sheet, INVESTMENT_EXCLUDING_TAX_ROW, OUTPUT_COLUMN)));
            response.setInvestmentIncludingTax(formatToTwoDecimalPlaces(ExcelFileOperationUtils.getCellDoubleValue(sheet, INVESTMENT_INCLUDING_TAX_ROW, OUTPUT_COLUMN)));

            log.debug("成功读取所有输出结果: 覆盖半径={}, 站距={}, 站点规模={}, 容量评估={}, 投资不含税={}, 投资含税={}",
                response.getNetworkCoverageRadius(), response.getNetworkStationDistance(),
                response.getCoverageAssessmentSiteScale(), response.getCapacityAssessmentResult(),
                response.getInvestmentExcludingTax(), response.getInvestmentIncludingTax());

            return response;
        } catch (Exception e) {
            log.error("读取输出结果失败", e);
            throw new ServiceException("读取计算结果失败: " + e.getMessage());
        }
    }






    /**
     * 格式化数值为两位小数
     */
    private Double formatToTwoDecimalPlaces(Double value) {
        if (value == null) {
            return null;
        }
        return new BigDecimal(value)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }

    /**
     * 获取临时文件统计信息
     */
    public TempFileManager.TempFileStatistics getTempFileStatistics() {
        return tempFileManager.getStatistics();
    }

    /**
     * 获取当前运行中的计算任务数量
     */
    @Override
    public int getRunningCalculationCount() {
        return runningCalculations.size();
    }

    /**
     * 取消指定任务的计算
     */
    @Override
    public boolean cancelCalculation(Long taskId) {
        Future<LowAltitudeCalculationResponse> future = runningCalculations.get(taskId);
        if (future != null) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                runningCalculations.remove(taskId);
                log.info("取消计算任务: {}", taskId);
            }
            return cancelled;
        }
        return false;
    }

    /**
     * 检查任务是否正在计算中
     */
    @Override
    public boolean isCalculating(Long taskId) {
        return runningCalculations.containsKey(taskId);
    }

    /**
     * 获取线程池状态信息
     */
    @Override
    public String getThreadPoolStatus() {
        if (calculationExecutor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor executor = (ThreadPoolExecutor) calculationExecutor;
            return String.format(
                "ThreadPool[Active: %d, Pool: %d, Queue: %d, Completed: %d]",
                executor.getActiveCount(),
                executor.getPoolSize(),
                executor.getQueue().size(),
                executor.getCompletedTaskCount()
            );
        }
        return "Unknown";
    }
}
