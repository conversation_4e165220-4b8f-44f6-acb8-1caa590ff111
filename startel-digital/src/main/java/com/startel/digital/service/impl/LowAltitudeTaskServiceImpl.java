package com.startel.digital.service.impl;

import com.startel.common.utils.DateUtils;
import com.startel.digital.domain.LowAltitudeTask;
import com.startel.digital.mapper.LowAltitudeTaskMapper;
import com.startel.digital.service.ILowAltitudeTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 低空规模计算任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-28
 */
@Service
public class LowAltitudeTaskServiceImpl implements ILowAltitudeTaskService {
    
    @Autowired
    private LowAltitudeTaskMapper lowAltitudeTaskMapper;

    /**
     * 查询低空规模计算任务
     * 
     * @param taskId 低空规模计算任务主键
     * @return 低空规模计算任务
     */
    @Override
    public LowAltitudeTask selectLowAltitudeTaskByTaskId(Long taskId) {
        return lowAltitudeTaskMapper.selectLowAltitudeTaskByTaskId(taskId);
    }

    /**
     * 查询低空规模计算任务列表
     * 
     * @param lowAltitudeTask 低空规模计算任务
     * @return 低空规模计算任务
     */
    @Override
    public List<LowAltitudeTask> selectLowAltitudeTaskList(LowAltitudeTask lowAltitudeTask) {
        return lowAltitudeTaskMapper.selectLowAltitudeTaskList(lowAltitudeTask);
    }

    /**
     * 新增低空规模计算任务
     * 
     * @param lowAltitudeTask 低空规模计算任务
     * @return 结果
     */
    @Override
    public int insertLowAltitudeTask(LowAltitudeTask lowAltitudeTask) {
        lowAltitudeTask.setCreateTime(DateUtils.getNowDate());
        return lowAltitudeTaskMapper.insertLowAltitudeTask(lowAltitudeTask);
    }

    /**
     * 修改低空规模计算任务
     * 
     * @param lowAltitudeTask 低空规模计算任务
     * @return 结果
     */
    @Override
    public int updateLowAltitudeTask(LowAltitudeTask lowAltitudeTask) {
        lowAltitudeTask.setUpdateTime(DateUtils.getNowDate());
        return lowAltitudeTaskMapper.updateLowAltitudeTask(lowAltitudeTask);
    }

    /**
     * 批量删除低空规模计算任务
     * 
     * @param taskIds 需要删除的低空规模计算任务主键
     * @return 结果
     */
    @Override
    public int deleteLowAltitudeTaskByTaskIds(Long[] taskIds) {
        return lowAltitudeTaskMapper.deleteLowAltitudeTaskByTaskIds(taskIds);
    }

    /**
     * 删除低空规模计算任务信息
     * 
     * @param taskId 低空规模计算任务主键
     * @return 结果
     */
    @Override
    public int deleteLowAltitudeTaskByTaskId(Long taskId) {
        return lowAltitudeTaskMapper.deleteLowAltitudeTaskByTaskId(taskId);
    }
}
