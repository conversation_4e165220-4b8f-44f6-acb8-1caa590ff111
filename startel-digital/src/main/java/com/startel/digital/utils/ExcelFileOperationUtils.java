package com.startel.digital.utils;

import com.startel.common.exception.ServiceException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * Excel文件操作工具类
 * 
 * @description 提供安全的Excel文件读写操作，支持文件锁机制
 * <AUTHOR>
 * @date 2024-12-28
 */
public class ExcelFileOperationUtils {
    
    private static final Logger log = LoggerFactory.getLogger(ExcelFileOperationUtils.class);
    
    // 文件锁超时时间（秒）
    private static final int LOCK_TIMEOUT_SECONDS = 60;

    // 重试次数
    private static final int MAX_RETRY_ATTEMPTS = 5;

    // 重试间隔（毫秒）
    private static final long RETRY_DELAY_MS = 2000;

    // 文件锁重试间隔（毫秒）
    private static final long LOCK_RETRY_DELAY_MS = 200;
    
    /**
     * 安全地执行Excel文件操作（带文件锁）
     * 
     * @param filePath Excel文件路径
     * @param operation 要执行的操作
     * @return 操作结果
     * @throws Exception 操作失败时抛出异常
     */
    public static <T> T executeWithFileLock(Path filePath, ExcelOperation<T> operation) throws Exception {
        validateFilePath(filePath);
        
        Exception lastException = null;
        
        // 重试机制
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                return executeWithFileLockInternal(filePath, operation, attempt);
            } catch (Exception e) {
                lastException = e;
                log.warn("Excel文件操作尝试 {}/{} 失败: {} - {}", 
                    attempt, MAX_RETRY_ATTEMPTS, filePath.getFileName(), e.getMessage());
                
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new ServiceException("操作被中断");
                    }
                }
            }
        }
        
        throw new ServiceException("Excel文件操作失败，已重试" + MAX_RETRY_ATTEMPTS + "次: " + lastException.getMessage());
    }

    /**
     * 兼容旧接口的方法
     */
    public static <T> T executeWithFileLock(Path filePath, ExcelOperationLegacy<T> operation) throws Exception {
        return executeWithFileLock(filePath, (path, raf, channel, lock) ->
            operation.execute(path, channel, lock));
    }
    
    /**
     * 内部执行方法
     */
    private static <T> T executeWithFileLockInternal(Path filePath, ExcelOperation<T> operation, int attempt) throws Exception {
        log.debug("开始Excel文件操作，尝试 {}: {}", attempt, filePath.getFileName());

        // 检查文件是否被其他进程占用
        if (!isFileAccessible(filePath)) {
            throw new ServiceException("文件被占用或无法访问: " + filePath);
        }

        RandomAccessFile raf = null;
        FileChannel channel = null;
        FileLock lock = null;

        try {
            // 使用RandomAccessFile获取可读写的FileChannel
            raf = new RandomAccessFile(filePath.toFile(), "rw");
            channel = raf.getChannel();

            if (channel == null) {
                throw new ServiceException("无法获取文件通道: " + filePath);
            }

            // 尝试获取文件锁（带超时）
            lock = tryLockWithTimeout(channel, LOCK_TIMEOUT_SECONDS);

            if (lock == null) {
                throw new ServiceException("获取文件锁超时，文件可能被其他进程占用: " + filePath);
            }

            log.debug("成功获取文件锁: {}", filePath.getFileName());

            // 执行Excel操作（传递RandomAccessFile以避免文件锁冲突）
            return operation.execute(filePath, raf, channel, lock);

        } catch (IOException e) {
            // 详细的错误分析
            String errorMsg = analyzeIOException(e, filePath);
            throw new ServiceException(errorMsg);

        } finally {
            // 按顺序释放资源
            if (lock != null && lock.isValid()) {
                try {
                    lock.release();
                    log.debug("释放文件锁: {}", filePath.getFileName());
                } catch (IOException e) {
                    log.warn("释放文件锁失败: {} - {}", filePath.getFileName(), e.getMessage());
                }
            }

            if (channel != null) {
                try {
                    channel.close();
                } catch (IOException e) {
                    log.warn("关闭文件通道失败: {} - {}", filePath.getFileName(), e.getMessage());
                }
            }

            if (raf != null) {
                try {
                    raf.close();
                } catch (IOException e) {
                    log.warn("关闭RandomAccessFile失败: {} - {}", filePath.getFileName(), e.getMessage());
                }
            }
        }
    }
    
    /**
     * 检查文件是否可访问
     */
    private static boolean isFileAccessible(Path filePath) {
        try {
            // 尝试以读写模式打开文件
            try (RandomAccessFile testRaf = new RandomAccessFile(filePath.toFile(), "rw")) {
                // 如果能成功打开，说明文件可访问
                return true;
            }
        } catch (IOException e) {
            log.debug("文件访问检查失败: {} - {}", filePath.getFileName(), e.getMessage());
            return false;
        }
    }

    /**
     * 分析IO异常并提供详细错误信息
     */
    private static String analyzeIOException(IOException e, Path filePath) {
        String message = e.getMessage();
        if (message == null) {
            return "文件操作失败: " + filePath;
        }

        message = message.toLowerCase();

        if (message.contains("being used by another process") ||
            message.contains("另一个程序正在使用") ||
            message.contains("已设定文件的一部分") ||
            message.contains("另一个程序已锁定文件的一部分")) {
            return "文件被其他程序占用或锁定，请关闭相关程序后重试: " + filePath;
        } else if (message.contains("access is denied") ||
                   message.contains("拒绝访问")) {
            return "文件访问权限不足，请检查文件权限: " + filePath;
        } else if (message.contains("no such file") ||
                   message.contains("找不到文件")) {
            return "文件不存在: " + filePath;
        } else if (message.contains("disk full") ||
                   message.contains("磁盘空间不足")) {
            return "磁盘空间不足，无法写入文件: " + filePath;
        } else if (message.contains("locked")) {
            return "文件被锁定，无法访问: " + filePath;
        } else {
            return "文件操作失败: " + message + " - " + filePath;
        }
    }

    /**
     * 带超时的文件锁获取（增强版）
     */
    private static FileLock tryLockWithTimeout(FileChannel channel, int timeoutSeconds) throws IOException {
        long startTime = System.currentTimeMillis();
        long timeoutMs = timeoutSeconds * 1000L;
        int attempts = 0;

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            attempts++;

            try {
                // 尝试获取独占锁
                FileLock lock = channel.tryLock();
                if (lock != null) {
                    log.debug("成功获取文件锁，尝试次数: {}", attempts);
                    return lock;
                }

                // 如果没有获取到锁，等待一段时间后重试
                Thread.sleep(LOCK_RETRY_DELAY_MS);

            } catch (IOException e) {
                String message = e.getMessage();
                if (message != null && (message.contains("locked") ||
                                      message.contains("being used") ||
                                      message.contains("另一个程序正在使用") ||
                                      message.contains("已设定文件的一部分"))) {
                    // 文件锁冲突，继续重试
                    try {
                        Thread.sleep(LOCK_RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("获取文件锁被中断", ie);
                    }
                } else {
                    // 其他IO异常，直接抛出
                    throw e;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IOException("获取文件锁被中断", e);
            }
        }

        log.warn("获取文件锁超时，总尝试次数: {}, 超时时间: {}秒", attempts, timeoutSeconds);
        return null; // 超时
    }
    
    /**
     * 验证文件路径
     */
    private static void validateFilePath(Path filePath) throws ServiceException {
        if (filePath == null) {
            throw new ServiceException("文件路径不能为null");
        }
        
        if (!Files.exists(filePath)) {
            throw new ServiceException("文件不存在: " + filePath);
        }
        
        if (!Files.isRegularFile(filePath)) {
            throw new ServiceException("不是有效的文件: " + filePath);
        }
        
        if (!Files.isReadable(filePath)) {
            throw new ServiceException("文件不可读: " + filePath);
        }
        
        if (!Files.isWritable(filePath)) {
            throw new ServiceException("文件不可写: " + filePath);
        }
    }
    
    /**
     * 安全地读取Excel工作簿（独立文件访问）
     */
    public static Workbook readWorkbook(Path filePath) throws IOException {
        validateFilePath(filePath);

        try (FileInputStream fis = new FileInputStream(filePath.toFile())) {
            return new XSSFWorkbook(fis);
        } catch (IOException e) {
            throw new IOException("读取Excel工作簿失败: " + analyzeIOException(e, filePath), e);
        }
    }

    /**
     * 基于已有FileChannel读取Excel工作簿（避免文件锁冲突）
     */
    public static Workbook readWorkbookFromChannel(Path filePath, FileChannel channel) throws IOException {
        if (channel == null) {
            throw new IllegalArgumentException("FileChannel不能为null");
        }

        try {
            // 重置通道位置到文件开始
            channel.position(0);

            // 使用Channels.newInputStream创建基于FileChannel的InputStream
            try (InputStream inputStream = java.nio.channels.Channels.newInputStream(channel)) {
                return new XSSFWorkbook(inputStream);
            }
        } catch (IOException e) {
            throw new IOException("从FileChannel读取Excel工作簿失败: " + analyzeIOException(e, filePath), e);
        }
    }

    /**
     * 基于RandomAccessFile读取Excel工作簿（最安全的方式）
     */
    public static Workbook readWorkbookFromRandomAccessFile(RandomAccessFile raf) throws IOException {
        if (raf == null) {
            throw new IllegalArgumentException("RandomAccessFile不能为null");
        }

        try {
            // 重置文件指针到开始位置
            raf.seek(0);

            // 创建基于RandomAccessFile的InputStream
            InputStream inputStream = new RandomAccessFileInputStream(raf);
            return new XSSFWorkbook(inputStream);

        } catch (IOException e) {
            throw new IOException("从RandomAccessFile读取Excel工作簿失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 安全地保存Excel工作簿（增强版）
     */
    public static void saveWorkbook(Workbook workbook, Path filePath) throws IOException {
        if (workbook == null) {
            throw new IllegalArgumentException("工作簿不能为null");
        }
        if (filePath == null) {
            throw new IllegalArgumentException("文件路径不能为null");
        }

        // 生成唯一的临时文件名
        String tempFileName = filePath.getFileName() + ".tmp." + System.currentTimeMillis();
        Path tempFile = filePath.getParent().resolve(tempFileName);

        try {
            // 先保存到临时文件
            try (FileOutputStream fos = new FileOutputStream(tempFile.toFile())) {
                workbook.write(fos);
                fos.flush();
                fos.getFD().sync(); // 强制刷新到磁盘
            }

            // 验证临时文件是否写入成功
            if (!Files.exists(tempFile) || Files.size(tempFile) == 0) {
                throw new IOException("临时文件写入失败: " + tempFile);
            }

            log.debug("临时文件写入成功: {}, 大小: {} bytes", tempFile.getFileName(), Files.size(tempFile));

            // 原子性地替换原文件
            Files.move(tempFile, filePath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);

            log.debug("文件保存成功: {}", filePath.getFileName());

        } catch (IOException e) {
            // 清理临时文件
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException cleanupException) {
                log.warn("清理临时文件失败: {} - {}", tempFile.getFileName(), cleanupException.getMessage());
            }

            throw new IOException("保存Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 基于RandomAccessFile保存Excel工作簿（避免文件锁冲突）
     */
    public static void saveWorkbookToRandomAccessFile(Workbook workbook, RandomAccessFile raf) throws IOException {
        if (workbook == null) {
            throw new IllegalArgumentException("工作簿不能为null");
        }
        if (raf == null) {
            throw new IllegalArgumentException("RandomAccessFile不能为null");
        }

        try {
            // 重置文件指针到开始位置
            raf.seek(0);

            // 创建基于RandomAccessFile的OutputStream
            try (RandomAccessFileOutputStream outputStream = new RandomAccessFileOutputStream(raf)) {
                workbook.write(outputStream);
                outputStream.flush();
            }

            // 截断文件到当前位置（移除可能的旧数据）
            raf.setLength(raf.getFilePointer());

            // 强制同步到磁盘
            raf.getFD().sync();

            log.debug("Excel工作簿保存成功到RandomAccessFile");

        } catch (IOException e) {
            throw new IOException("保存Excel工作簿到RandomAccessFile失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 设置单元格值（安全版本）
     */
    public static void setCellValue(Sheet sheet, int rowIndex, int columnIndex, Object value) {
        if (sheet == null) {
            throw new IllegalArgumentException("工作表不能为null");
        }
        
        Row row = sheet.getRow(rowIndex);
        if (row == null) {
            row = sheet.createRow(rowIndex);
        }
        
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }
        
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }
    
    /**
     * 获取单元格值（安全版本）
     */
    public static Object getCellValue(Sheet sheet, int rowIndex, int columnIndex) {
        if (sheet == null) {
            return null;
        }
        
        Row row = sheet.getRow(rowIndex);
        if (row == null) {
            return null;
        }
        
        Cell cell = row.getCell(columnIndex);
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                try {
                    // 尝试获取计算后的值
                    switch (cell.getCachedFormulaResultType()) {
                        case STRING:
                            return cell.getStringCellValue();
                        case NUMERIC:
                            return cell.getNumericCellValue();
                        case BOOLEAN:
                            return cell.getBooleanCellValue();
                        default:
                            return cell.getStringCellValue();
                    }
                } catch (Exception e) {
                    log.warn("获取公式单元格值失败: {}", e.getMessage());
                    return null;
                }
            case BLANK:
            case _NONE:
            default:
                return null;
        }
    }
    
    /**
     * 获取单元格数值（安全版本）
     */
    public static Double getCellDoubleValue(Sheet sheet, int rowIndex, int columnIndex) {
        Object value = getCellValue(sheet, rowIndex, columnIndex);
        if (value == null) {
            return null;
        }
        
        if (value instanceof Double) {
            return (Double) value;
        } else if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为数值: {}", value);
                return null;
            }
        }
        
        return null;
    }
    
    /**
     * 获取单元格整数值（安全版本）
     */
    public static Integer getCellIntegerValue(Sheet sheet, int rowIndex, int columnIndex) {
        Double doubleValue = getCellDoubleValue(sheet, rowIndex, columnIndex);
        return doubleValue != null ? doubleValue.intValue() : null;
    }
    
    /**
     * 获取单元格字符串值（安全版本）
     */
    public static String getCellStringValue(Sheet sheet, int rowIndex, int columnIndex) {
        Object value = getCellValue(sheet, rowIndex, columnIndex);
        return value != null ? value.toString() : null;
    }
    
    /**
     * Excel操作接口（增强版，包含RandomAccessFile）
     */
    @FunctionalInterface
    public interface ExcelOperation<T> {
        T execute(Path filePath, RandomAccessFile raf, FileChannel channel, FileLock lock) throws Exception;
    }

    /**
     * Excel操作接口（兼容版本）
     */
    @FunctionalInterface
    public interface ExcelOperationLegacy<T> {
        T execute(Path filePath, FileChannel channel, FileLock lock) throws Exception;
    }

    /**
     * RandomAccessFile到InputStream的适配器
     */
    private static class RandomAccessFileInputStream extends InputStream {
        private final RandomAccessFile raf;
        private final long startPosition;

        public RandomAccessFileInputStream(RandomAccessFile raf) throws IOException {
            this.raf = raf;
            this.startPosition = raf.getFilePointer();
        }

        @Override
        public int read() throws IOException {
            return raf.read();
        }

        @Override
        public int read(byte[] b) throws IOException {
            return raf.read(b);
        }

        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            return raf.read(b, off, len);
        }

        @Override
        public long skip(long n) throws IOException {
            long currentPos = raf.getFilePointer();
            long fileLength = raf.length();
            long newPos = Math.min(currentPos + n, fileLength);
            raf.seek(newPos);
            return newPos - currentPos;
        }

        @Override
        public int available() throws IOException {
            long remaining = raf.length() - raf.getFilePointer();
            return (int) Math.min(remaining, Integer.MAX_VALUE);
        }

        @Override
        public void close() throws IOException {
            // 不关闭RandomAccessFile，因为它可能还在使用
            // 只重置到开始位置
            raf.seek(startPosition);
        }

        @Override
        public boolean markSupported() {
            return false;
        }
    }

    /**
     * RandomAccessFile到OutputStream的适配器
     */
    private static class RandomAccessFileOutputStream extends OutputStream {
        private final RandomAccessFile raf;

        public RandomAccessFileOutputStream(RandomAccessFile raf) {
            this.raf = raf;
        }

        @Override
        public void write(int b) throws IOException {
            raf.write(b);
        }

        @Override
        public void write(byte[] b) throws IOException {
            raf.write(b);
        }

        @Override
        public void write(byte[] b, int off, int len) throws IOException {
            raf.write(b, off, len);
        }

        @Override
        public void flush() throws IOException {
            // RandomAccessFile没有flush方法，但可以调用getFD().sync()
            raf.getFD().sync();
        }

        @Override
        public void close() throws IOException {
            // 不关闭RandomAccessFile，因为它可能还在使用
            // 只确保数据已写入
            flush();
        }
    }
}
