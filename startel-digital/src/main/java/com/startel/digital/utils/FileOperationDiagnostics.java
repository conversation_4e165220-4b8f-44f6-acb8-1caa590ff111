package com.startel.digital.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件操作诊断工具
 * 
 * @description 用于诊断文件操作问题，提供详细的系统信息
 * <AUTHOR>
 * @date 2024-12-28
 */
public class FileOperationDiagnostics {
    
    private static final Logger log = LoggerFactory.getLogger(FileOperationDiagnostics.class);
    
    /**
     * 诊断文件操作问题
     */
    public static Map<String, Object> diagnoseFileOperation(Path filePath) {
        Map<String, Object> diagnosis = new HashMap<>();
        
        try {
            // 基本文件信息
            diagnosis.put("filePath", filePath.toAbsolutePath().toString());
            diagnosis.put("fileName", filePath.getFileName().toString());
            diagnosis.put("fileExists", Files.exists(filePath));
            
            if (Files.exists(filePath)) {
                File file = filePath.toFile();
                diagnosis.put("fileSize", Files.size(filePath));
                diagnosis.put("canRead", file.canRead());
                diagnosis.put("canWrite", file.canWrite());
                diagnosis.put("canExecute", file.canExecute());
                diagnosis.put("isFile", file.isFile());
                diagnosis.put("isDirectory", file.isDirectory());
                diagnosis.put("isHidden", file.isHidden());
                diagnosis.put("lastModified", file.lastModified());
            }
            
            // 父目录信息
            Path parentDir = filePath.getParent();
            if (parentDir != null) {
                diagnosis.put("parentExists", Files.exists(parentDir));
                diagnosis.put("parentWritable", Files.isWritable(parentDir));
                diagnosis.put("parentReadable", Files.isReadable(parentDir));
                
                try {
                    diagnosis.put("parentFreeSpace", parentDir.toFile().getFreeSpace());
                    diagnosis.put("parentTotalSpace", parentDir.toFile().getTotalSpace());
                } catch (Exception e) {
                    diagnosis.put("parentSpaceError", e.getMessage());
                }
            }
            
            // 文件锁测试
            if (Files.exists(filePath)) {
                diagnosis.putAll(testFileLock(filePath));
            }
            
            // 系统信息
            diagnosis.put("osName", System.getProperty("os.name"));
            diagnosis.put("osVersion", System.getProperty("os.version"));
            diagnosis.put("javaVersion", System.getProperty("java.version"));
            diagnosis.put("userDir", System.getProperty("user.dir"));
            diagnosis.put("tempDir", System.getProperty("java.io.tmpdir"));
            
            // JVM内存信息
            Runtime runtime = Runtime.getRuntime();
            diagnosis.put("maxMemory", runtime.maxMemory());
            diagnosis.put("totalMemory", runtime.totalMemory());
            diagnosis.put("freeMemory", runtime.freeMemory());
            diagnosis.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            
        } catch (Exception e) {
            diagnosis.put("diagnosisError", e.getMessage());
            log.error("文件诊断失败", e);
        }
        
        return diagnosis;
    }
    
    /**
     * 测试文件锁
     */
    private static Map<String, Object> testFileLock(Path filePath) {
        Map<String, Object> lockTest = new HashMap<>();
        
        try {
            // 测试读取访问
            try (RandomAccessFile raf = new RandomAccessFile(filePath.toFile(), "r")) {
                lockTest.put("canOpenForRead", true);
            } catch (Exception e) {
                lockTest.put("canOpenForRead", false);
                lockTest.put("readError", e.getMessage());
            }
            
            // 测试写入访问
            try (RandomAccessFile raf = new RandomAccessFile(filePath.toFile(), "rw")) {
                lockTest.put("canOpenForWrite", true);
                
                // 测试文件锁
                FileChannel channel = raf.getChannel();
                if (channel != null) {
                    lockTest.put("channelAvailable", true);
                    
                    try (FileLock lock = channel.tryLock()) {
                        if (lock != null) {
                            lockTest.put("canLock", true);
                            lockTest.put("lockValid", lock.isValid());
                            lockTest.put("lockShared", lock.isShared());
                        } else {
                            lockTest.put("canLock", false);
                            lockTest.put("lockReason", "tryLock returned null");
                        }
                    } catch (Exception e) {
                        lockTest.put("canLock", false);
                        lockTest.put("lockError", e.getMessage());
                    }
                } else {
                    lockTest.put("channelAvailable", false);
                }
            } catch (Exception e) {
                lockTest.put("canOpenForWrite", false);
                lockTest.put("writeError", e.getMessage());
            }
            
        } catch (Exception e) {
            lockTest.put("lockTestError", e.getMessage());
        }
        
        return lockTest;
    }
    
    /**
     * 打印诊断信息
     */
    public static void printDiagnosis(Path filePath) {
        log.info("=== 文件操作诊断报告 ===");
        Map<String, Object> diagnosis = diagnoseFileOperation(filePath);
        
        for (Map.Entry<String, Object> entry : diagnosis.entrySet()) {
            log.info("{}: {}", entry.getKey(), entry.getValue());
        }
        log.info("=== 诊断报告结束 ===");
    }
    
    /**
     * 检查文件是否被占用
     */
    public static boolean isFileInUse(Path filePath) {
        if (!Files.exists(filePath)) {
            return false;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(filePath.toFile(), "rw");
             FileChannel channel = raf.getChannel();
             FileLock lock = channel.tryLock()) {
            
            return lock == null;
            
        } catch (IOException e) {
            String message = e.getMessage();
            if (message != null) {
                message = message.toLowerCase();
                return message.contains("being used") || 
                       message.contains("另一个程序正在使用") ||
                       message.contains("已设定文件的一部分") ||
                       message.contains("locked");
            }
            return true;
        }
    }
    
    /**
     * 等待文件可用
     */
    public static boolean waitForFileAvailable(Path filePath, int timeoutSeconds) {
        long startTime = System.currentTimeMillis();
        long timeoutMs = timeoutSeconds * 1000L;
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (!isFileInUse(filePath)) {
                return true;
            }
            
            try {
                Thread.sleep(500); // 等待500ms
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        return false;
    }
    
    /**
     * 强制释放文件（谨慎使用）
     */
    public static boolean forceReleaseFile(Path filePath) {
        try {
            // 尝试通过垃圾回收释放可能的文件句柄
            System.gc();
            Thread.sleep(100);
            
            // 检查文件是否可用
            return !isFileInUse(filePath);
            
        } catch (Exception e) {
            log.warn("强制释放文件失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取文件操作建议
     */
    public static String getFileOperationAdvice(Path filePath, Exception exception) {
        StringBuilder advice = new StringBuilder();
        advice.append("文件操作失败建议：\n");
        
        String errorMsg = exception.getMessage();
        if (errorMsg != null) {
            errorMsg = errorMsg.toLowerCase();
            
            if (errorMsg.contains("being used") || 
                errorMsg.contains("另一个程序正在使用") ||
                errorMsg.contains("已设定文件的一部分")) {
                advice.append("1. 文件被其他程序占用，请关闭Excel或其他可能使用该文件的程序\n");
                advice.append("2. 检查是否有其他计算任务正在使用同一文件\n");
                advice.append("3. 等待一段时间后重试\n");
            }
            
            if (errorMsg.contains("access denied") || 
                errorMsg.contains("拒绝访问")) {
                advice.append("1. 检查文件权限，确保应用程序有读写权限\n");
                advice.append("2. 以管理员身份运行应用程序\n");
                advice.append("3. 检查文件是否为只读属性\n");
            }
            
            if (errorMsg.contains("disk full") || 
                errorMsg.contains("磁盘空间不足")) {
                advice.append("1. 清理磁盘空间\n");
                advice.append("2. 检查临时文件目录的可用空间\n");
            }
        }
        
        advice.append("4. 检查系统资源使用情况\n");
        advice.append("5. 重启应用程序或系统\n");
        
        return advice.toString();
    }
}
