package com.startel.digital.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.CodeSource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 临时文件管理工具类
 * 
 * @description 智能管理临时计算文件的存储位置，支持多种部署环境
 * <AUTHOR>
 * @date 2024-12-28
 */
@Component
public class TempFileManager {
    
    private static final Logger log = LoggerFactory.getLogger(TempFileManager.class);
    
    private static final String TEMP_FOLDER_NAME = "temp-calculations";
    private static final String FALLBACK_FOLDER_NAME = "startel-digital-temp";
    
    private Path tempDirectory;
    private boolean initialized = false;
    
    @PostConstruct
    public void init() {
        initializeTempDirectory();
    }
    
    /**
     * 初始化临时目录
     */
    private void initializeTempDirectory() {
        List<Path> candidatePaths = Arrays.asList(
            // 第一优先：项目根目录下的temp-calculations
            getProjectRootPath().resolve(TEMP_FOLDER_NAME),
            // 第二优先：当前工作目录下的temp-calculations
            Paths.get(System.getProperty("user.dir")).resolve(TEMP_FOLDER_NAME),
            // 第三优先：用户目录下的.startel/temp-calculations
            Paths.get(System.getProperty("user.home")).resolve(".startel").resolve(TEMP_FOLDER_NAME),
            // 第四优先：系统临时目录下的专用文件夹
            Paths.get(System.getProperty("java.io.tmpdir")).resolve(FALLBACK_FOLDER_NAME)
        );
        
        for (Path candidatePath : candidatePaths) {
            if (tryCreateDirectory(candidatePath)) {
                this.tempDirectory = candidatePath;
                this.initialized = true;
                log.info("临时文件目录初始化成功: {}", tempDirectory.toAbsolutePath());
                return;
            }
        }
        
        throw new RuntimeException("无法创建临时文件目录，请检查文件系统权限");
    }
    
    /**
     * 获取项目根目录路径
     */
    private Path getProjectRootPath() {
        try {
            // 尝试通过类路径定位项目根目录
            CodeSource codeSource = TempFileManager.class.getProtectionDomain().getCodeSource();
            if (codeSource != null) {
                URL location = codeSource.getLocation();
                Path jarPath = Paths.get(location.toURI());
                
                if (jarPath.toString().endsWith(".jar")) {
                    // JAR部署：返回JAR文件所在目录
                    return jarPath.getParent();
                } else {
                    // 开发环境：向上查找到项目根目录
                    Path currentPath = jarPath;
                    while (currentPath != null && currentPath.getParent() != null) {
                        if (Files.exists(currentPath.resolve("pom.xml")) || 
                            Files.exists(currentPath.resolve("build.gradle"))) {
                            return currentPath;
                        }
                        currentPath = currentPath.getParent();
                    }
                }
            }
        } catch (Exception e) {
            log.debug("无法通过代码源定位项目根目录: {}", e.getMessage());
        }
        
        // 备选方案：返回当前工作目录
        return Paths.get(System.getProperty("user.dir"));
    }
    
    /**
     * 尝试创建目录
     */
    private boolean tryCreateDirectory(Path path) {
        try {
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
            
            // 验证读写权限
            if (Files.isWritable(path) && Files.isReadable(path)) {
                // 创建测试文件验证权限
                Path testFile = path.resolve("test_" + System.currentTimeMillis() + ".tmp");
                Files.createFile(testFile);
                Files.delete(testFile);
                
                log.debug("成功验证目录权限: {}", path.toAbsolutePath());
                return true;
            } else {
                log.debug("目录权限不足: {}", path.toAbsolutePath());
                return false;
            }
        } catch (Exception e) {
            log.debug("创建目录失败: {} - {}", path.toAbsolutePath(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取临时目录路径
     */
    public Path getTempDirectory() {
        if (!initialized) {
            throw new IllegalStateException("临时文件管理器未初始化");
        }
        return tempDirectory;
    }
    
    /**
     * 获取临时目录路径字符串
     */
    public String getTempDirectoryPath() {
        return getTempDirectory().toAbsolutePath().toString();
    }
    
    /**
     * 创建临时文件路径
     */
    public Path createTempFilePath(String fileName) {
        return getTempDirectory().resolve(fileName);
    }
    
    /**
     * 创建带UUID的临时计算文件路径
     */
    public Path createCalculationTempFilePath(String uuid) {
        String fileName = "calculation_" + uuid + ".xlsx";
        return createTempFilePath(fileName);
    }
    
    /**
     * 清理过期的临时文件
     */
    public int cleanupExpiredFiles(int maxAgeHours) {
        if (!initialized) {
            log.warn("临时文件管理器未初始化，跳过清理");
            return 0;
        }
        
        AtomicInteger cleanedCount = new AtomicInteger();
        long maxAgeMillis = maxAgeHours * 3600 * 1000L;
        long currentTime = System.currentTimeMillis();
        
        try {
            Files.walk(tempDirectory)
                .filter(Files::isRegularFile)
                .filter(path -> path.getFileName().toString().startsWith("calculation_"))
                .filter(path -> {
                    try {
                        long fileTime = Files.getLastModifiedTime(path).toMillis();
                        return (currentTime - fileTime) > maxAgeMillis;
                    } catch (IOException e) {
                        return false;
                    }
                })
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        cleanedCount.getAndIncrement();
                        log.debug("清理过期临时文件: {}", path.getFileName());
                    } catch (IOException e) {
                        log.warn("删除过期临时文件失败: {} - {}", path.getFileName(), e.getMessage());
                    }
                });
                
        } catch (IOException e) {
            log.error("清理临时文件时发生异常", e);
        }
        
        if (cleanedCount.get() > 0) {
            log.info("清理了 {} 个过期临时文件", cleanedCount);
        }
        
        return cleanedCount.get();
    }
    
    /**
     * 获取临时文件统计信息
     */
    public TempFileStatistics getStatistics() {
        if (!initialized) {
            return new TempFileStatistics(0, 0, 0, "未初始化");
        }
        
        try {
            long totalFiles = Files.walk(tempDirectory)
                .filter(Files::isRegularFile)
                .filter(path -> path.getFileName().toString().startsWith("calculation_"))
                .count();
                
            long totalSize = Files.walk(tempDirectory)
                .filter(Files::isRegularFile)
                .filter(path -> path.getFileName().toString().startsWith("calculation_"))
                .mapToLong(path -> {
                    try {
                        return Files.size(path);
                    } catch (IOException e) {
                        return 0;
                    }
                })
                .sum();
                
            long freeSpace = tempDirectory.toFile().getFreeSpace();
            
            return new TempFileStatistics(totalFiles, totalSize, freeSpace, tempDirectory.toAbsolutePath().toString());
            
        } catch (IOException e) {
            log.error("获取临时文件统计信息失败", e);
            return new TempFileStatistics(0, 0, 0, tempDirectory.toAbsolutePath().toString());
        }
    }
    
    /**
     * 验证临时目录状态
     */
    public boolean validateTempDirectory() {
        if (!initialized) {
            return false;
        }
        
        try {
            // 检查目录是否存在
            if (!Files.exists(tempDirectory)) {
                log.error("临时目录不存在: {}", tempDirectory);
                return false;
            }
            
            // 检查读写权限
            if (!Files.isWritable(tempDirectory) || !Files.isReadable(tempDirectory)) {
                log.error("临时目录权限不足: {}", tempDirectory);
                return false;
            }
            
            // 检查磁盘空间（至少100MB）
            long freeSpace = tempDirectory.toFile().getFreeSpace();
            if (freeSpace < 100 * 1024 * 1024) {
                log.warn("临时目录磁盘空间不足: {} MB", freeSpace / 1024 / 1024);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证临时目录失败", e);
            return false;
        }
    }
    
    /**
     * 临时文件统计信息类
     */
    public static class TempFileStatistics {
        private final long fileCount;
        private final long totalSize;
        private final long freeSpace;
        private final String directory;
        
        public TempFileStatistics(long fileCount, long totalSize, long freeSpace, String directory) {
            this.fileCount = fileCount;
            this.totalSize = totalSize;
            this.freeSpace = freeSpace;
            this.directory = directory;
        }
        
        public long getFileCount() { return fileCount; }
        public long getTotalSize() { return totalSize; }
        public long getFreeSpace() { return freeSpace; }
        public String getDirectory() { return directory; }
        
        @Override
        public String toString() {
            return String.format("TempFileStatistics{files=%d, size=%d bytes, free=%d MB, dir='%s'}", 
                fileCount, totalSize, freeSpace / 1024 / 1024, directory);
        }
    }
}
