<template>
  <div class="low-altitude-calculation">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-content">
        <div class="logo">🛰️ 低空规模计算系统</div>
        <div class="user-info">
          <span>👤 {{ $store.state.user.name || '用户' }}</span>
          <span>📅 {{ currentDate }}</span>
          <el-button type="text" size="small" @click="showHelp">帮助</el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 左侧任务列表 -->
      <div class="task-panel">
        <div class="panel-header">
          <div class="panel-title">📋 计算任务 ({{ total }})</div>
          <el-button type="primary" size="small" @click="createNewTask">➕ 新建</el-button>
        </div>
        
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="🔍 搜索任务..."
            size="small"
            @input="handleSearch"
            clearable
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>

        <div class="filter-box">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-select v-model="queryParams.taskStatus" placeholder="状态筛选" size="small" @change="loadTaskList" clearable>
                <el-option label="待计算" value="0"></el-option>
                <el-option label="计算中" value="1"></el-option>
                <el-option label="计算成功" value="2"></el-option>
                <el-option label="计算失败" value="3"></el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-select v-model="queryParams.type" placeholder="类型筛选" size="small" @change="loadTaskList" clearable>
                <el-option label="航线" value="航线"></el-option>
                <el-option label="全空间立体覆盖" value="全空间立体覆盖"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </div>
        
        <div class="task-list" v-loading="taskListLoading">
          <div
            v-for="task in taskList"
            :key="task.taskId"
            :class="['task-item', { active: selectedTask && selectedTask.taskId === task.taskId }]"
            @click="selectTask(task)"
          >
            <div class="task-name">{{ task.taskName }}</div>
            <div class="task-meta">
              <span>⏱️ {{ formatDate(task.createTime) }}</span>
              <el-tag :type="getStatusTagType(task.taskStatus)" size="mini">
                {{ getStatusText(task.taskStatus) }}
              </el-tag>
            </div>
            <div class="task-type">{{ task.type }} | {{ task.technicalSystem }}</div>
            <div class="task-actions" @click.stop>
              <el-button type="text" size="mini" @click="editTask(task)" icon="el-icon-edit">编辑</el-button>
              <el-button type="text" size="mini" @click="duplicateTask(task)" icon="el-icon-copy-document">复制</el-button>
              <el-button type="text" size="mini" @click="deleteTask(task)" icon="el-icon-delete" style="color: #f56c6c;">删除</el-button>
            </div>
          </div>
          
          <div v-if="taskList.length === 0 && !taskListLoading" class="empty-state">
            <el-empty description="暂无任务数据">
              <el-button type="primary" @click="createNewTask">创建第一个任务</el-button>
            </el-empty>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-box" v-if="total > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next"
            :total="total"
            small
          />
        </div>
      </div>

      <!-- 中央表单区域 -->
      <div class="form-panel">
        <div class="form-header">
          <h3>{{ selectedTask ? '编辑任务' : '新建任务' }}</h3>
          <div class="form-actions-header">
            <el-button size="small" @click="resetForm" icon="el-icon-refresh">重置</el-button>
            <el-button size="small" @click="loadTemplate" icon="el-icon-document">模板</el-button>
          </div>
        </div>
        
        <div class="form-container">
          <el-form
            ref="taskForm"
            :model="taskForm"
            :rules="taskRules"
            label-width="140px"
            size="small"
          >
            <!-- 任务信息 -->
            <div class="form-section">
              <div class="section-title">📝 任务信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="任务名称" prop="taskName">
                    <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" maxlength="100" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="创建时间">
                    <el-input v-model="taskForm.createTime" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="任务描述">
                    <el-input
                      v-model="taskForm.taskDescription"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入任务描述"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 基本参数 -->
            <div class="form-section">
              <div class="section-title">🔧 基本参数</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="类型" prop="type">
                    <el-select v-model="taskForm.type" placeholder="请选择类型" style="width: 100%">
                      <el-option label="航线" value="航线" />
                      <el-option label="全空间立体覆盖" value="全空间立体覆盖" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="区域面积/航线长度" prop="areaOrLength">
                    <el-input-number
                      v-model="taskForm.areaOrLength"
                      :min="0.01"
                      :precision="2"
                      placeholder="请输入数值"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="航线高度" prop="routeHeight">
                    <el-input
                      v-model="taskForm.routeHeight"
                      placeholder="格式：50-100"
                    >
                      <template slot="append">米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 技术参数 -->
            <div class="form-section">
              <div class="section-title">⚙️ 技术参数</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="技术体制" prop="technicalSystem">
                    <el-radio-group v-model="taskForm.technicalSystem">
                      <el-radio label="4G">4G</el-radio>
                      <el-radio label="5G">5G</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="使用频点" prop="frequency">
                    <el-input-number
                      v-model="taskForm.frequency"
                      :min="0.01"
                      :precision="2"
                      placeholder="请输入频点"
                      style="width: 100%"
                    />
                    <span class="input-unit">MHz</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="SS RSRP发射功率" prop="transmitPower">
                    <el-input-number
                      v-model="taskForm.transmitPower"
                      :precision="2"
                      placeholder="请输入功率"
                      style="width: 100%"
                    />
                    <span class="input-unit">dBm</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="SS RSRP接入门限功率" prop="accessThresholdPower">
                    <el-input-number
                      v-model="taskForm.accessThresholdPower"
                      :precision="2"
                      placeholder="请输入门限功率"
                      style="width: 100%"
                    />
                    <span class="input-unit">dBm</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 业务参数 -->
            <div class="form-section">
              <div class="section-title">💼 业务参数</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="业务保障" prop="businessSupport">
                    <el-select v-model="taskForm.businessSupport" placeholder="请选择业务保障" style="width: 100%">
                      <el-option label="8K" value="8K" />
                      <el-option label="4K" value="4K" />
                      <el-option label="360P" value="360P" />
                      <el-option label="720P" value="720P" />
                      <el-option label="1080p" value="1080p" />
                      <el-option label="控制信息" value="控制信息" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="无人机密度" prop="droneDensity">
                    <el-input-number
                      v-model="taskForm.droneDensity"
                      :min="0"
                      placeholder="请输入密度"
                      style="width: 100%"
                    />
                    <span class="input-unit">架/km²</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="无线环境场景" prop="wirelessEnvironment">
                    <el-radio-group v-model="taskForm.wirelessEnvironment">
                      <el-radio label="郊区农村">郊区农村</el-radio>
                      <el-radio label="城区">城区</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="业务类型" prop="businessType">
                    <el-input v-model="taskForm.businessType" placeholder="请输入业务类型" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="占比" prop="proportion">
                    <div class="slider-container">
                      <span class="slider-label">0.0</span>
                      <el-slider
                        v-model="taskForm.proportion"
                        :min="0"
                        :max="1"
                        :step="0.1"
                        :format-tooltip="formatTooltip"
                        style="margin: 0 15px; flex: 1;"
                      />
                      <span class="slider-label">1.0</span>
                      <el-input-number
                        v-model="taskForm.proportion"
                        :min="0"
                        :max="1"
                        :step="0.1"
                        :precision="1"
                        size="small"
                        style="width: 80px; margin-left: 10px;"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
              <el-button
                type="primary"
                :loading="calculating"
                @click="calculateTask"
                :disabled="!canCalculate"
                icon="el-icon-cpu"
              >
                {{ calculating ? '计算中...' : '开始计算' }}
              </el-button>
              <el-button type="success" @click="saveTask" icon="el-icon-check">保存方案</el-button>
              <el-button @click="saveAndCalculate" :loading="calculating" icon="el-icon-lightning">
                保存并计算
              </el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 右侧结果面板 -->
      <div class="result-panel">
        <div class="result-header">
          <h3>📊 计算结果</h3>
          <div v-if="calculationResult" class="result-status">
            <el-tag :type="getStatusTagType(selectedTask?.taskStatus)" size="small">
              {{ getStatusText(selectedTask?.taskStatus) }}
            </el-tag>
            <div class="result-time">
              ⏱️ {{ formatDateTime(selectedTask?.calculateEndTime) }}
            </div>
          </div>
        </div>

        <div class="result-container">
          <div v-if="calculationResult" class="result-content">
            <!-- 覆盖参数卡片 -->
            <div class="result-card coverage-card">
              <div class="card-header">
                <i class="el-icon-position"></i>
                <span>覆盖参数</span>
              </div>
              <div class="card-content">
                <div class="result-item">
                  <div class="item-label">布网覆盖半径</div>
                  <div class="item-value">
                    {{ formatNumber(calculationResult.networkCoverageRadius) }}
                    <span class="item-unit">米</span>
                  </div>
                </div>
                <div class="result-item">
                  <div class="item-label">布网站距</div>
                  <div class="item-value">
                    {{ formatNumber(calculationResult.networkStationDistance) }}
                    <span class="item-unit">米</span>
                  </div>
                </div>
                <div class="result-item">
                  <div class="item-label">站点规模</div>
                  <div class="item-value">
                    {{ calculationResult.coverageAssessmentSiteScale }}
                    <span class="item-unit">个</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 容量评估卡片 -->
            <div class="result-card capacity-card">
              <div class="card-header">
                <i class="el-icon-data-analysis"></i>
                <span>容量评估</span>
              </div>
              <div class="card-content">
                <div class="result-item">
                  <div class="item-label">评估结果</div>
                  <div class="item-value" :class="getCapacityResultClass(calculationResult.capacityAssessmentResult)">
                    <i :class="getCapacityIcon(calculationResult.capacityAssessmentResult)"></i>
                    {{ calculationResult.capacityAssessmentResult }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 投资成本卡片 -->
            <div class="result-card investment-card">
              <div class="card-header">
                <i class="el-icon-money"></i>
                <span>投资成本</span>
              </div>
              <div class="card-content">
                <div class="result-item">
                  <div class="item-label">投资（不含税）</div>
                  <div class="item-value">
                    {{ formatCurrency(calculationResult.investmentExcludingTax) }}
                    <span class="item-unit">元</span>
                  </div>
                </div>
                <div class="result-item">
                  <div class="item-label">投资（含税）</div>
                  <div class="item-value">
                    {{ formatCurrency(calculationResult.investmentIncludingTax) }}
                    <span class="item-unit">元</span>
                  </div>
                </div>
                <div class="result-item">
                  <div class="item-label">税额</div>
                  <div class="item-value tax-amount">
                    {{ formatCurrency(getTaxAmount()) }}
                    <span class="item-unit">元</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 结果操作按钮 -->
            <div class="result-actions">
              <el-button type="primary" size="small" @click="exportExcel" icon="el-icon-download">
                导出Excel
              </el-button>
              <el-button size="small" @click="copyResult" icon="el-icon-copy-document">
                复制结果
              </el-button>
              <el-button size="small" @click="generateReport" icon="el-icon-document">
                生成报告
              </el-button>
              <el-button size="small" @click="recalculate" icon="el-icon-refresh">
                重新计算
              </el-button>
            </div>
          </div>

          <div v-else class="empty-result">
            <el-empty description="暂无计算结果" :image-size="120">
              <el-button type="primary" @click="calculateTask" :disabled="!canCalculate">
                开始计算
              </el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="closeDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dialogForm"
        :model="dialogForm"
        :rules="dialogRules"
        label-width="120px"
        size="small"
      >
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="dialogForm.taskName" placeholder="请输入任务名称" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input
            v-model="dialogForm.taskDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDialog" :loading="dialogSubmitting">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 模板选择对话框 -->
    <el-dialog
      title="选择参数模板"
      :visible.sync="templateDialogVisible"
      width="500px"
    >
      <div class="template-list">
        <div
          v-for="template in parameterTemplates"
          :key="template.id"
          class="template-item"
          @click="applyTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-desc">{{ template.description }}</div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="templateDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listTask,
  getTask,
  addTask,
  updateTask,
  delTask,
  calculateTask,
  saveAndCalculateTask,
  getTaskStatistics
} from '@/api/lowAltitudeCalculation'

export default {
  name: 'LowAltitudeCalculationOptimized',
  data() {
    return {
      // 当前日期
      currentDate: '',

      // 任务列表相关
      taskList: [],
      taskListLoading: false,
      selectedTask: null,
      searchKeyword: '',
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        taskStatus: null,
        type: null
      },
      total: 0,

      // 表单相关
      taskForm: this.getInitialTaskForm(),

      // 表单验证规则
      taskRules: {
        taskName: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        areaOrLength: [
          { required: true, message: '区域面积/航线长度不能为空', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '必须大于0', trigger: 'blur' }
        ],
        routeHeight: [
          { required: true, message: '航线高度不能为空', trigger: 'blur' },
          { pattern: /^\d+-\d+$/, message: '格式不正确，应为两个数字用-连接，如50-100', trigger: 'blur' }
        ],
        businessSupport: [
          { required: true, message: '请选择业务保障', trigger: 'change' }
        ],
        wirelessEnvironment: [
          { required: true, message: '请选择无线环境场景', trigger: 'change' }
        ],
        technicalSystem: [
          { required: true, message: '请选择技术体制', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '使用频点不能为空', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '必须大于0', trigger: 'blur' }
        ],
        transmitPower: [
          { required: true, message: 'SS RSRP发射功率不能为空', trigger: 'blur' }
        ],
        droneDensity: [
          { required: true, message: '无人机密度不能为空', trigger: 'blur' },
          { type: 'number', min: 0, message: '不能小于0', trigger: 'blur' }
        ],
        businessType: [
          { required: true, message: '业务类型不能为空', trigger: 'blur' }
        ],
        proportion: [
          { required: true, message: '占比不能为空', trigger: 'blur' },
          { type: 'number', min: 0, max: 1, message: '占比必须在0-1之间', trigger: 'blur' }
        ],
        accessThresholdPower: [
          { required: true, message: 'SS RSRP接入门限功率不能为空', trigger: 'blur' }
        ]
      },

      // 计算相关
      calculating: false,
      calculationResult: null,

      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      dialogForm: {
        taskId: null,
        taskName: '',
        taskDescription: ''
      },
      dialogRules: {
        taskName: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      },
      dialogSubmitting: false,
      isEdit: false,

      // 模板相关
      templateDialogVisible: false,
      parameterTemplates: [
        {
          id: 1,
          name: '城区5G高清视频',
          description: '城区环境，5G网络，4K视频传输',
          params: {
            type: '航线',
            areaOrLength: 100,
            routeHeight: '50-100',
            businessSupport: '4K',
            wirelessEnvironment: '城区',
            technicalSystem: '5G',
            frequency: 2600,
            transmitPower: 46,
            droneDensity: 10,
            businessType: '视频传输',
            proportion: 0.8,
            accessThresholdPower: -110
          }
        },
        {
          id: 2,
          name: '郊区4G数据采集',
          description: '郊区环境，4G网络，数据采集业务',
          params: {
            type: '全空间立体覆盖',
            areaOrLength: 500,
            routeHeight: '100-200',
            businessSupport: '1080p',
            wirelessEnvironment: '郊区农村',
            technicalSystem: '4G',
            frequency: 1800,
            transmitPower: 43,
            droneDensity: 20,
            businessType: '数据采集',
            proportion: 0.6,
            accessThresholdPower: -105
          }
        },
        {
          id: 3,
          name: '城区5G控制信息',
          description: '城区环境，5G网络，无人机控制',
          params: {
            type: '航线',
            areaOrLength: 50,
            routeHeight: '30-80',
            businessSupport: '控制信息',
            wirelessEnvironment: '城区',
            technicalSystem: '5G',
            frequency: 3500,
            transmitPower: 40,
            droneDensity: 5,
            businessType: '遥控指令',
            proportion: 1.0,
            accessThresholdPower: -115
          }
        }
      ]
    }
  },

  computed: {
    // 是否可以计算
    canCalculate() {
      return this.selectedTask && this.selectedTask.taskStatus !== 1 && !this.calculating
    }
  },

  mounted() {
    this.updateCurrentDate()
    this.loadTaskList()
    // 每分钟更新一次时间
    setInterval(this.updateCurrentDate, 60000)
  },

  methods: {
    // 获取初始表单数据
    getInitialTaskForm() {
      return {
        taskId: null,
        taskName: '',
        taskDescription: '',
        taskStatus: 0,
        type: '',
        areaOrLength: null,
        routeHeight: '',
        businessSupport: '',
        wirelessEnvironment: '',
        technicalSystem: '',
        frequency: null,
        transmitPower: null,
        droneDensity: null,
        businessType: '',
        proportion: 0.8,
        accessThresholdPower: null,
        createTime: ''
      }
    },
